package ptype.basic;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.typedef.PDefString;
import ptype.types.PTString;
import ptype.types.PTInteger;
import ptype.types.PTBoolean;
import ptype.types.PTFloat;
import ptype.types.PTDouble;

public class PTStringTest {

    @Test
    @DisplayName("Should create PTString with default definition")
    void testDefaultConstructor() {
        PTString string = new PTString(PDefString.DefaultDef);
        
        assertNotNull(string);
        assertEquals(PDefString.DefaultDef, string.getTypeDef());
        assertNotNull(string.getValue());
        assertEquals("", string.getAsString()); // Default should be empty string
    }

    @Test
    @DisplayName("Should create PTString with definition and value")
    void testConstructorWithValue() {
        String testValue = "Hello World";
        PTString string = new PTString(PDefString.DefaultDef, testValue);
        
        assertNotNull(string);
        assertEquals(testValue, string.getAsString());
        assertEquals(PDefString.DefaultDef, string.getTypeDef());
    }

    @Test
    @DisplayName("Should create PTString with value using convenience constructor")
    void testConvenienceConstructor() {
        String testValue = "Test String";
        PTString string = new PTString(testValue);
        
        assertNotNull(string);
        assertEquals(testValue, string.getAsString());
        assertEquals(PDefString.DefaultDef, string.getTypeDef());
    }

    @Test
    @DisplayName("Should set and get values correctly")
    void testSetAndGetValue() {
        PTString string = new PTString(PDefString.DefaultDef);

        String[] testValues = {"", "Hello", "World", "Test123", "Special chars: !@#$%", "Unicode: 🚀"};

        for (String value : testValues) {
            string.setFromString(value);
            assertEquals(value, string.getAsString());
        }
    }

    @Test
    @DisplayName("Should represent as boolean correctly")
    void testBooleanRepresentation() {
        PTString string = new PTString(PDefString.DefaultDef);

        // Test empty string - canRepresentAsBoolean should be false for empty string
        string.setFromString("");
        assertFalse(string.canRepresentAsBoolean());

        // Test "true" string
        string.setFromString("true");
        assertTrue(string.canRepresentAsBoolean());
        assertTrue(string.getAsBoolean());

        // Test "false" string
        string.setFromString("false");
        assertTrue(string.canRepresentAsBoolean());
        assertFalse(string.getAsBoolean());

        // Test numeric string
        string.setFromString("0");
        assertTrue(string.canRepresentAsBoolean());
        assertFalse(string.getAsBoolean()); // 0 is false

        string.setFromString("1");
        assertTrue(string.canRepresentAsBoolean());
        assertTrue(string.getAsBoolean()); // 1 is true
    }

    @Test
    @DisplayName("Should set from boolean correctly")
    void testSetFromBoolean() {
        PTString string = new PTString(PDefString.DefaultDef);
        
        string.setFromBoolean(true);
        assertEquals("true", string.getAsString());
        
        string.setFromBoolean(false);
        assertEquals("false", string.getAsString());
    }

    @Test
    @DisplayName("Should represent as integer correctly")
    void testIntegerRepresentation() {
        PTString string = new PTString("42");
        
        assertTrue(string.canRepresentAsInteger());
        assertEquals(42, string.getAsLong());
        
        string.setFromLong(123);
        assertEquals("123", string.getAsString());
        assertEquals(123, string.getAsLong());

        string.setFromLong(-456);
        assertEquals("-456", string.getAsString());
        assertEquals(-456, string.getAsLong());
    }

    @Test
    @DisplayName("Should represent as double correctly")
    void testDoubleRepresentation() {
        PTString string = new PTString("42.5");
        
        assertTrue(string.canRepresentAsDouble());
        assertEquals(42.5, string.getAsDouble(), 0.001);
        
        string.setFromDouble(123.456);
        assertEquals("123.456", string.getAsString());
        assertEquals(123.456, string.getAsDouble(), 0.001);
        
        string.setFromDouble(-78.9);
        assertEquals("-78.9", string.getAsString());
        assertEquals(-78.9, string.getAsDouble(), 0.001);
    }

    @Test
    @DisplayName("Should represent as string correctly")
    void testStringRepresentation() {
        PTString string = new PTString(PDefString.DefaultDef);
        string.setFromString("test");

        assertTrue(string.canRepresentAsString());
        assertEquals("test", string.getAsString());

        string.setFromString("Another test");
        assertEquals("Another test", string.getAsString());

        string.setFromString("");
        assertEquals("", string.getAsString());
    }

    @Test
    @DisplayName("Should compare to other strings correctly")
    void testTypeCompareTo() {
        PTString string1 = new PTString("apple");
        PTString string2 = new PTString("banana");
        PTString string3 = new PTString("apple");
        
        // Test less than
        assertTrue(string1.typeCompareTo(string2) < 0);
        
        // Test greater than
        assertTrue(string2.typeCompareTo(string1) > 0);
        
        // Test equal
        assertEquals(0, string1.typeCompareTo(string3));
        
        // Test with other types that can represent as string
        PTInteger intType = new PTInteger(42);
        PTBoolean boolType = new PTBoolean(true);
        
        // These should work since they can represent as string
        assertNotEquals(0, string1.typeCompareTo(intType));
        assertNotEquals(0, string1.typeCompareTo(boolType));
    }

    @Test
    @DisplayName("Should handle type conversion from other PTTypes")
    void testSetFromType() {
        PTString string = new PTString(PDefString.DefaultDef);
        
        // Test from another PTString
        PTString sourceString = new PTString("source");
        assertTrue(string.canRepresentAsType(sourceString));
        string.setFromType(sourceString);
        assertEquals("source", string.getAsString());
        
        // Test from PTInteger
        PTInteger sourceInt = new PTInteger(42);
        assertTrue(string.canRepresentAsType(sourceInt));
        string.setFromType(sourceInt);
        assertEquals("42", string.getAsString());
        
        // Test from PTBoolean
        PTBoolean sourceBool = new PTBoolean(true);
        assertTrue(string.canRepresentAsType(sourceBool));
        string.setFromType(sourceBool);
        assertEquals("true", string.getAsString());
        
        // Test from PTFloat
        PTFloat sourceFloat = new PTFloat(3.14f);
        assertTrue(string.canRepresentAsType(sourceFloat));
        string.setFromType(sourceFloat);
        // Note: Float precision causes 3.14f to be represented as 3.140000104904175
        assertTrue(string.getAsString().startsWith("3.14"));
        
        // Test from PTDouble
        PTDouble sourceDouble = new PTDouble(2.718);
        assertTrue(string.canRepresentAsType(sourceDouble));
        string.setFromType(sourceDouble);
        assertEquals("2.718", string.getAsString());
    }

    @Test
    @DisplayName("Should work with custom PDefString")
    void testWithCustomDefinition() {
        PDefString customDef = new PDefString(50); // 50 character max
        PTString string = new PTString(customDef);

        assertEquals(customDef, string.getTypeDef());

        string.setFromString("Custom string");
        assertEquals("Custom string", string.getAsString());
    }

    @Test
    @DisplayName("Should validate correctly")
    void testValidation() {
        PTString string = new PTString("test");
        assertTrue(string.isValid());

        // Test with custom definition
        PDefString customDef = new PDefString(10);
        PTString customString = new PTString(customDef);
        customString.setFromString("short");
        assertTrue(customString.isValid());
    }

    @Test
    @DisplayName("Should handle edge cases")
    void testEdgeCases() {
        PTString string = new PTString(PDefString.DefaultDef);

        // Test with null-like values
        string.setFromString("");
        assertEquals("", string.getAsString());

        // Test with very long string
        StringBuilder longString = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longString.append("a");
        }
        string.setFromString(longString.toString());
        assertEquals(longString.toString(), string.getAsString());

        // Test with special characters
        string.setFromString("Line1\nLine2\tTabbed");
        assertEquals("Line1\nLine2\tTabbed", string.getAsString());

        // Test numeric string conversions
        string.setFromString("123");
        assertEquals(123, string.getAsLong());
        
        string.setFromString("45.67");
        assertEquals(45.67, string.getAsDouble(), 0.001);
    }

    @Test
    @DisplayName("Should handle string comparison edge cases")
    void testStringComparisonEdgeCases() {
        PTString empty1 = new PTString("");
        PTString empty2 = new PTString("");
        PTString nonEmpty = new PTString("a");
        
        // Empty strings should be equal
        assertEquals(0, empty1.typeCompareTo(empty2));
        
        // Empty vs non-empty
        assertTrue(empty1.typeCompareTo(nonEmpty) < 0);
        assertTrue(nonEmpty.typeCompareTo(empty1) > 0);
        
        // Case sensitivity
        PTString lower = new PTString("apple");
        PTString upper = new PTString("APPLE");
        assertNotEquals(0, lower.typeCompareTo(upper));
        
        // Numeric strings
        PTString num1 = new PTString("10");
        PTString num2 = new PTString("2");
        // String comparison, not numeric: "10" < "2"
        assertTrue(num1.typeCompareTo(num2) < 0);
    }
}
