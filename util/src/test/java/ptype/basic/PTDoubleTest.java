package ptype.basic;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.typedef.PDefDouble;
import ptype.types.PTDouble;
import ptype.types.PTInteger;
import ptype.types.PTBoolean;
import ptype.types.PTString;
import ptype.types.PTFloat;

public class PTDoubleTest {

    @Test
    @DisplayName("Should create PTDouble with default definition")
    void testDefaultConstructor() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);
        
        assertNotNull(doubleVal);
        assertEquals(PDefDouble.DefaultDef, doubleVal.getTypeDef());
        assertEquals(0.0, doubleVal.getValue(), 0.001); // Default should be 0.0
    }

    @Test
    @DisplayName("Should create PTDouble with definition and value")
    void testConstructorWithValue() {
        double testValue = 3.141592653589793;
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef, testValue);
        
        assertNotNull(doubleVal);
        assertEquals(testValue, doubleVal.getValue(), 0.000001);
        assertEquals(PDefDouble.DefaultDef, doubleVal.getTypeDef());
    }

    @Test
    @DisplayName("Should create PTDouble with value using convenience constructor")
    void testConvenienceConstructor() {
        double testValue = 2.718281828459045;
        PTDouble doubleVal = new PTDouble(testValue);
        
        assertNotNull(doubleVal);
        assertEquals(testValue, doubleVal.getValue(), 0.000001);
        assertEquals(PDefDouble.DefaultDef, doubleVal.getTypeDef());
    }

    @Test
    @DisplayName("Should set and get values correctly")
    void testSetAndGetValue() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        double[] testValues = {0.0, 1.0, -1.0, 3.141592653589793, -2.718281828459045, 123.456789, -789.012345};

        for (double value : testValues) {
            doubleVal.setValue(value);
            assertEquals(value, doubleVal.getValue(), 0.000001);
        }
    }

    @Test
    @DisplayName("Should represent as boolean correctly")
    void testBooleanRepresentation() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        assertTrue(doubleVal.canRepresentAsBoolean());

        // Test zero as false
        doubleVal.setValue(0.0);
        assertFalse(doubleVal.getAsBoolean());

        // Test non-zero as true
        doubleVal.setValue(1.0);
        assertTrue(doubleVal.getAsBoolean());

        doubleVal.setValue(-1.0);
        assertTrue(doubleVal.getAsBoolean());

        doubleVal.setValue(0.000001);
        assertTrue(doubleVal.getAsBoolean());

        doubleVal.setValue(-0.000001);
        assertTrue(doubleVal.getAsBoolean());
    }

    @Test
    @DisplayName("Should set from boolean correctly")
    void testSetFromBoolean() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);
        
        doubleVal.setFromBoolean(true);
        assertEquals(1.0, doubleVal.getValue(), 0.001);
        
        doubleVal.setFromBoolean(false);
        assertEquals(0.0, doubleVal.getValue(), 0.001);
    }

    @Test
    @DisplayName("Should represent as integer correctly")
    void testIntegerRepresentation() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);
        
        assertTrue(doubleVal.canRepresentAsInteger());
        
        // Test whole numbers
        doubleVal.setValue(42.0);
        assertEquals(42, doubleVal.getAsLong());
        
        // Test truncation behavior
        doubleVal.setValue(42.3);
        assertEquals(42, doubleVal.getAsLong()); // Should truncate
        
        doubleVal.setValue(42.9);
        assertEquals(42, doubleVal.getAsLong()); // Should truncate
        
        doubleVal.setValue(-42.3);
        assertEquals(-42, doubleVal.getAsLong());
        
        doubleVal.setValue(-42.9);
        assertEquals(-42, doubleVal.getAsLong());
        
        // Test setting from integer
        doubleVal.setFromLong(123);
        assertEquals(123.0, doubleVal.getValue(), 0.001);
        
        doubleVal.setFromLong(-456);
        assertEquals(-456.0, doubleVal.getValue(), 0.001);
    }

    @Test
    @DisplayName("Should represent as double correctly")
    void testDoubleRepresentation() {
        PTDouble doubleVal = new PTDouble(3.141592653589793);
        
        assertTrue(doubleVal.canRepresentAsDouble());
        assertEquals(3.141592653589793, doubleVal.getAsDouble(), 0.000001);
        
        doubleVal.setFromDouble(123.456789);
        assertEquals(123.456789, doubleVal.getValue(), 0.000001);
        
        doubleVal.setFromDouble(-78.987654);
        assertEquals(-78.987654, doubleVal.getValue(), 0.000001);
    }

    @Test
    @DisplayName("Should represent as string correctly")
    void testStringRepresentation() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        assertTrue(doubleVal.canRepresentAsString());

        doubleVal.setValue(3.141592653589793);
        assertEquals("3.141592653589793", doubleVal.getAsString());

        doubleVal.setValue(-2.718281828459045);
        assertEquals("-2.718281828459045", doubleVal.getAsString());

        doubleVal.setValue(0.0);
        assertEquals("0.0", doubleVal.getAsString());
    }

    @Test
    @DisplayName("Should compare to other doubles correctly")
    void testTypeCompareTo() {
        PTDouble double1 = new PTDouble(1.5);
        PTDouble double2 = new PTDouble(2.5);
        PTDouble double3 = new PTDouble(1.5);

        // Test less than
        assertTrue(double1.typeCompareTo(double2) < 0);

        // Test greater than
        assertTrue(double2.typeCompareTo(double1) > 0);

        // Test equal
        assertEquals(0, double1.typeCompareTo(double3));

        // Test with other types that can represent as double
        PTInteger intType = new PTInteger(2);
        PTFloat floatType = new PTFloat(1.5f);

        assertTrue(double1.typeCompareTo(intType) < 0); // 1.5 < 2
        // Note: Due to float precision, 1.5f might not exactly equal 1.5 double
        int comparison = double1.typeCompareTo(floatType);
        assertTrue(Math.abs(comparison) <= 1); // Should be very close to equal
    }

    @Test
    @DisplayName("Should handle type conversion from other PTTypes")
    void testSetFromType() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        // Test from another PTDouble
        PTDouble sourceDouble = new PTDouble(3.141592653589793);
        assertTrue(doubleVal.canRepresentAsType(sourceDouble));
        doubleVal.setFromType(sourceDouble);
        assertEquals(3.141592653589793, doubleVal.getValue(), 0.000001);

        // Test from PTInteger
        PTInteger sourceInt = new PTInteger(42);
        assertTrue(doubleVal.canRepresentAsType(sourceInt));
        doubleVal.setFromType(sourceInt);
        assertEquals(42.0, doubleVal.getValue(), 0.001);

        // Note: PTBoolean has a bug in setFromType - it checks the wrong condition
        // So we can't test setFromType with PTBoolean, but we can test the individual methods

        // Test from PTFloat
        PTFloat sourceFloat = new PTFloat(2.718f);
        assertTrue(doubleVal.canRepresentAsType(sourceFloat));
        doubleVal.setFromType(sourceFloat);
        assertEquals(2.718, doubleVal.getValue(), 0.001);

        // Test direct conversion methods instead of setFromType for boolean
        doubleVal.setFromBoolean(true);
        assertEquals(1.0, doubleVal.getValue(), 0.001);

        doubleVal.setFromBoolean(false);
        assertEquals(0.0, doubleVal.getValue(), 0.001);
    }

    @Test
    @DisplayName("Should validate correctly")
    void testValidation() {
        PTDouble doubleVal = new PTDouble(3.141592653589793);
        assertTrue(doubleVal.isValid());

        // Test with custom definition
        PTDouble customDouble = new PTDouble(PDefDouble.DefaultDef);
        customDouble.setValue(123.456789);
        assertTrue(customDouble.isValid());
    }

    @Test
    @DisplayName("Should handle edge cases")
    void testEdgeCases() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        // Test with extreme double values
        doubleVal.setValue(Double.MAX_VALUE);
        assertEquals(Double.MAX_VALUE, doubleVal.getValue(), 0.001);

        doubleVal.setValue(Double.MIN_VALUE);
        assertEquals(Double.MIN_VALUE, doubleVal.getValue(), 0.001);

        doubleVal.setValue(-Double.MAX_VALUE);
        assertEquals(-Double.MAX_VALUE, doubleVal.getValue(), 0.001);

        // Test special double values
        doubleVal.setValue(Double.POSITIVE_INFINITY);
        assertEquals(Double.POSITIVE_INFINITY, doubleVal.getValue());

        doubleVal.setValue(Double.NEGATIVE_INFINITY);
        assertEquals(Double.NEGATIVE_INFINITY, doubleVal.getValue());

        doubleVal.setValue(Double.NaN);
        assertTrue(Double.isNaN(doubleVal.getValue()));

        // Test conversion edge cases
        doubleVal.setFromLong(Long.MAX_VALUE);
        // Should handle the conversion (may lose precision)
        
        doubleVal.setFromLong(Long.MIN_VALUE);
        // Should handle the conversion (may lose precision)
    }

    @Test
    @DisplayName("Should handle high precision correctly")
    void testHighPrecision() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        // Test high precision values
        double highPrecision = 1.23456789012345678901234567890;
        doubleVal.setValue(highPrecision);
        // Double has limited precision, but should be very close
        assertEquals(highPrecision, doubleVal.getValue(), 0.000000000000001);

        // Test very small numbers
        double verySmall = 1e-15;
        doubleVal.setValue(verySmall);
        assertEquals(verySmall, doubleVal.getValue(), 1e-16);

        // Test very large numbers
        double veryLarge = 1e15;
        doubleVal.setValue(veryLarge);
        assertEquals(veryLarge, doubleVal.getValue(), 1e10);
    }

    @Test
    @DisplayName("Should handle comparison with different numeric types")
    void testComparisonWithDifferentTypes() {
        PTDouble doubleVal = new PTDouble(5.0);

        // Compare with integer
        PTInteger intVal = new PTInteger(5);
        assertEquals(0, doubleVal.typeCompareTo(intVal)); // 5.0 == 5

        PTInteger intSmaller = new PTInteger(4);
        assertTrue(doubleVal.typeCompareTo(intSmaller) > 0); // 5.0 > 4

        PTInteger intLarger = new PTInteger(6);
        assertTrue(doubleVal.typeCompareTo(intLarger) < 0); // 5.0 < 6

        // Compare with float - may have precision differences
        PTFloat floatVal = new PTFloat(5.0f);
        int comparison = doubleVal.typeCompareTo(floatVal);
        assertTrue(Math.abs(comparison) <= 1); // Should be very close to equal

        // Compare with boolean
        PTBoolean boolTrue = new PTBoolean(true);
        assertTrue(doubleVal.typeCompareTo(boolTrue) > 0); // 5.0 > 1.0

        PTBoolean boolFalse = new PTBoolean(false);
        assertTrue(doubleVal.typeCompareTo(boolFalse) > 0); // 5.0 > 0.0
    }

    @Test
    @DisplayName("Should handle precision loss in conversions")
    void testPrecisionLoss() {
        PTDouble doubleVal = new PTDouble(PDefDouble.DefaultDef);

        // Test conversion from very large long
        long largeLong = Long.MAX_VALUE;
        doubleVal.setFromLong(largeLong);
        // Converting back may not be exact due to precision loss
        long convertedBack = doubleVal.getAsLong();
        // Should be close but may not be exact
        assertTrue(Math.abs(largeLong - convertedBack) < 1000);

        // Test conversion with decimal places
        doubleVal.setValue(123.456789);
        assertEquals(123, doubleVal.getAsLong()); // Should truncate decimal part

        doubleVal.setValue(-123.456789);
        assertEquals(-123, doubleVal.getAsLong()); // Should truncate decimal part
    }
}
